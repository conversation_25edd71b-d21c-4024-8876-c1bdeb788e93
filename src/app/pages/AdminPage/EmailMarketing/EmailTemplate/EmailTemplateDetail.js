import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Form, Input, Select, Row, Col, Tabs, Button, Card, Modal } from "antd";
import { ArrowLeftOutlined, SaveOutlined, EyeOutlined } from "@ant-design/icons";

import { toast } from "@component/ToastProvider";
import Loading from "@component/Loading";
import { AntForm } from "@component/AntForm";
import AntButton from "@component/AntButton";
import RULE from "@rule";
import { LINK } from "@link";
import { BUTTON } from "@constant";

import { getTemplateDetail, createTemplate, updateTemplate } from "@services/EmailMarketing";

import "../EmailMarketing.scss";
import "./EmailTemplateDetail.scss";

const { TabPane } = Tabs;

const EmailTemplateDetail = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams();

  const [form] = Form.useForm();
  const [isLoading, setLoading] = useState(false);
  const [htmlContent, setHtmlContent] = useState("");
  const [previewMode, setPreviewMode] = useState(false);
  const [previewData, setPreviewData] = useState({});
  const [showPreviewModal, setShowPreviewModal] = useState(false);

  // Định nghĩa các danh mục mẫu email
  const TEMPLATE_CATEGORIES = [
    { value: "welcome", label: t("TEMPLATE_CATEGORY_WELCOME") },
    { value: "promotion", label: t("TEMPLATE_CATEGORY_PROMOTION") },
    { value: "reminder", label: t("TEMPLATE_CATEGORY_REMINDER") },
    { value: "notification", label: t("TEMPLATE_CATEGORY_NOTIFICATION") },
    { value: "other", label: t("TEMPLATE_CATEGORY_OTHER") }
  ];

  // Định nghĩa các biến có sẵn
  const AVAILABLE_VARIABLES = [
    { name: "{name}", description: "Tên khách hàng", defaultValue: "Nguyễn Văn A" },
    { name: "{trial_end_date}", description: "Ngày hết hạn dùng thử", defaultValue: "31/12/2024" },
    { name: "{personal_link}", description: "Đường dẫn cá nhân hoá", defaultValue: "https://example.com/renew/abc123" },
    { name: "{email}", description: "Email khách hàng", defaultValue: "<EMAIL>" },
    { name: "{company_name}", description: "Tên công ty", defaultValue: "Công ty ABC" },
    { name: "{support_email}", description: "Email hỗ trợ", defaultValue: "<EMAIL>" },
    { name: "{phone}", description: "Số điện thoại", defaultValue: "0123456789" },
    { name: "{package_name}", description: "Tên gói dịch vụ", defaultValue: "Gói Premium" },
    { name: "{registration_date}", description: "Ngày đăng ký", defaultValue: "01/01/2024" },
    { name: "{unsubscribe_link}", description: "Liên kết hủy đăng ký", defaultValue: "https://example.com/unsubscribe" }
  ];

  // Lấy dữ liệu mẫu email khi id thay đổi
  useEffect(() => {
    if (!isCreating) {
      getTemplateData();
    } else {
      // Đặt giá trị mặc định cho mẫu mới
      form.setFieldsValue({
        category: "welcome"
      });
      setHtmlContent("");
      setLoading(false);
    }
  }, [id]);

  const getTemplateData = async () => {
    setLoading(true);
    try {
      const data = await getTemplateDetail(id);
      if (data) {
        form.setFieldsValue(data);
        setHtmlContent(data.content || "");
      } else {
        toast.error(t("ERROR_FETCHING_TEMPLATE"));
      }
    } catch (error) {
      console.error('Error loading template data:', error);
      toast.error(t("ERROR_FETCHING_TEMPLATE"));
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (values) => {
    try {
      setLoading(true);
      const saveData = {
        ...values,
        content: htmlContent
      };

      let response;
      if (isCreating) {
        response = await createTemplate(saveData);
        if (response) {
          toast.success(t("CREATE_TEMPLATE_SUCCESS"));
          navigate(LINK.ADMIN.EMAIL_TEMPLATE);
        } else {
          toast.error(t("CREATE_TEMPLATE_ERROR"));
        }
      } else {
        saveData._id = id;
        response = await updateTemplate(saveData);
        if (response) {
          toast.success(t("UPDATE_TEMPLATE_SUCCESS"));
          navigate(LINK.ADMIN.EMAIL_TEMPLATE);
        } else {
          toast.error(t("UPDATE_TEMPLATE_ERROR"));
        }
      }
    } catch (error) {
      console.error('Error saving template:', error);
      toast.error(t("ERROR_SAVING_TEMPLATE"));
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate(LINK.ADMIN.EMAIL_TEMPLATE);
  };

  const insertVariable = (variable) => {
    // Thêm biến vào nội dung HTML tại vị trí con trỏ
    setHtmlContent((prevContent) => {
      return prevContent + " " + variable.name + " ";
    });
  };

  const togglePreview = () => {
    setPreviewMode(!previewMode);
  };

  // Khởi tạo dữ liệu mẫu mặc định
  useEffect(() => {
    const defaultData = {};
    AVAILABLE_VARIABLES.forEach(variable => {
      const key = variable.name.replace(/[{}]/g, '');
      defaultData[key] = variable.defaultValue;
    });
    setPreviewData(defaultData);
  }, []);

  // Function thay thế biến trong nội dung
  const replaceVariables = (content, data) => {
    let replacedContent = content;
    Object.keys(data).forEach(key => {
      const placeholder = `{${key}}`;
      const value = data[key] || placeholder;
      replacedContent = replacedContent.replace(new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'), value);
    });
    return replacedContent;
  };

  // Hiển thị modal preview với dữ liệu mẫu
  const showPreview = () => {
    setShowPreviewModal(true);
  };

  // Cập nhật dữ liệu preview
  const handlePreviewDataChange = (key, value) => {
    setPreviewData(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const isCreating = !id || id === "create";

  return (
    <Loading active={isLoading} transparent>
      <div className="email-template-detail-container">
        <div className="email-template-detail__header">
          <div className="header-content">
            <div className="header-title">{isCreating ? t("CREATE_NEW_TEMPLATE") : t("EDIT_TEMPLATE")}</div>
            <div className="header-description">{t("EMAIL_TEMPLATE_MANAGEMENT_DESCRIPTION")}</div>
          </div>
        </div>

        <div className="template-info-container">
          <div className="info-header">
            <h3 className="section-title">{t("TEMPLATE_INFORMATION")}</h3>
          </div>
          <div className="info-content">
            <AntForm
              form={form}
              onFinish={handleSave}
              layout="vertical"
              requiredMark={true}
              className="form-template-info"
            >
              <div className="form-row">
                <AntForm.Item
                  name="name"
                  label={t("TEMPLATE_NAME")}
                  rules={[{ required: true, message: t("PLEASE_ENTER_TEMPLATE_NAME") }]}
                  className="title-field"
                >
                  <Input placeholder={t("ENTER_TEMPLATE_NAME")} />
                </AntForm.Item>

                <AntForm.Item
                  name="category"
                  label={t("CATEGORY")}
                  rules={[{ required: true, message: t("PLEASE_SELECT_CATEGORY") }]}
                  className="category-field"
                >
                  <Select
                    placeholder={t("SELECT_CATEGORY")}
                    options={TEMPLATE_CATEGORIES}
                  />
                </AntForm.Item>
              </div>

              <div className="form-row">
                <AntForm.Item
                  name="subject"
                  label={t("EMAIL_SUBJECT")}
                  rules={[{ required: true, message: t("PLEASE_ENTER_EMAIL_SUBJECT") }]}
                  className="title-field"
                >
                  <Input placeholder={t("ENTER_EMAIL_SUBJECT")} />
                </AntForm.Item>
              </div>
            </AntForm>
          </div>
        </div>

        <div className="template-content-container">
          <div className="info-header">
            <h3 className="section-title">{t("CONTENT")}</h3>
          </div>
          <div className="info-content">
            <div className="email-variables-container">
              <div className="email-variables-title">{t("AVAILABLE_VARIABLES")}:</div>
              <div className="email-variables-list">
                {AVAILABLE_VARIABLES.map((variable, index) => (
                  <div key={index} className="variable-item">
                    <Button
                      size="small"
                      onClick={() => insertVariable(variable)}
                      className="variable-button"
                      type="dashed"
                    >
                      {variable.name}
                    </Button>
                    <span className="variable-description">{variable.description}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="email-preview-toggle">
              <AntButton
                onClick={togglePreview}
                type={BUTTON.LIGHT_NAVY}
                size="middle"
                className="preview-toggle-button"
                style={{ marginRight: '12px' }}
              >
                {previewMode ? t("EDIT_MODE") : t("PREVIEW_MODE")}
              </AntButton>
              <AntButton
                onClick={showPreview}
                type={BUTTON.DEEP_NAVY}
                size="middle"
                className="preview-button"
                icon={<EyeOutlined />}
              >
                {t("PREVIEW_EMAIL")}
              </AntButton>
            </div>

            {previewMode ? (
              <div
                className="email-preview"
                dangerouslySetInnerHTML={{ __html: htmlContent }}
              />
            ) : (
              <Input.TextArea
                value={htmlContent}
                onChange={(e) => setHtmlContent(e.target.value)}
                autoSize={{ minRows: 10, maxRows: 20 }}
                placeholder={t("ENTER_EMAIL_CONTENT_HTML")}
                className="email-content-editor"
              />
            )}
          </div>
        </div>

        <div className="template-content-container">
          <div className="info-header">
            <h3 className="section-title">{t("TEST_SEND")}</h3>
          </div>
          <div className="info-content">
            <div className="test-email-container">
              <div className="test-email-label">{t("TEST_EMAIL")}</div>
              <div className="test-email-input-group">
                <Input
                  placeholder={t("ENTER_TEST_EMAIL")}
                  className="test-email-input"
                  name="testEmail"
                />
                <AntButton
                  type={BUTTON.DEEP_NAVY}
                  size="middle"
                  className="test-email-button"
                >
                  {t("SEND_TEST_EMAIL")}
                </AntButton>
              </div>
            </div>
          </div>
        </div>

        <div className="save-button-container">
          <AntButton
            type={BUTTON.LIGHT_NAVY}
            size="large"
            onClick={handleCancel}
            style={{ marginRight: '16px' }}
            className="cancel-button"
          >
            {t("CANCEL")}
          </AntButton>
          <AntButton
            type={BUTTON.DEEP_NAVY}
            size="large"
            onClick={handleSave}
            className="save-button"
            icon={<SaveOutlined />}
          >
            {isCreating ? t("CREATE") : t("SAVE")}
          </AntButton>
        </div>

        {/* Modal Preview Email */}
        <Modal
          title={t("PREVIEW_EMAIL")}
          open={showPreviewModal}
          onCancel={() => setShowPreviewModal(false)}
          width={900}
          footer={[
            <AntButton key="close" onClick={() => setShowPreviewModal(false)}>
              {t("CLOSE")}
            </AntButton>
          ]}
          className="email-preview-modal"
        >
          <div className="preview-modal-content">
            {/* Form nhập dữ liệu mẫu */}
            <div className="preview-data-form">
              <h4>{t("SAMPLE_DATA")}</h4>
              <Row gutter={[16, 16]}>
                {AVAILABLE_VARIABLES.map((variable, index) => {
                  const key = variable.name.replace(/[{}]/g, '');
                  return (
                    <Col span={12} key={index}>
                      <div className="preview-input-group">
                        <label className="preview-label">
                          {variable.name} - {variable.description}
                        </label>
                        <Input
                          value={previewData[key] || ''}
                          onChange={(e) => handlePreviewDataChange(key, e.target.value)}
                          placeholder={variable.defaultValue}
                          size="small"
                        />
                      </div>
                    </Col>
                  );
                })}
              </Row>
            </div>

            {/* Preview email */}
            <div className="preview-email-content">
              <h4>{t("EMAIL_PREVIEW")}</h4>

              {/* Subject preview */}
              <div className="preview-subject">
                <strong>{t("SUBJECT")}: </strong>
                <span>{replaceVariables(form.getFieldValue('subject') || '', previewData)}</span>
              </div>

              {/* Content preview */}
              <div className="preview-content">
                <div
                  className="email-content-preview"
                  dangerouslySetInnerHTML={{
                    __html: replaceVariables(htmlContent, previewData)
                  }}
                />
              </div>
            </div>
          </div>
        </Modal>
      </div>
    </Loading>
  );
};

export default EmailTemplateDetail;
