import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Form, Input, Select, Row, Col, Tabs, <PERSON><PERSON>, Card } from "antd";
import { ArrowLeftOutlined, SaveOutlined } from "@ant-design/icons";

import { toast } from "@component/ToastProvider";
import Loading from "@component/Loading";
import { AntForm } from "@component/AntForm";
import AntButton from "@component/AntButton";
import RULE from "@rule";
import { LINK } from "@link";
import { BUTTON } from "@constant";

import { getTemplateDetail, createTemplate, updateTemplate } from "@services/EmailMarketing";

import "../EmailMarketing.scss";
import "./EmailTemplateDetail.scss";

const { TabPane } = Tabs;

const EmailTemplateDetail = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams();

  const [form] = Form.useForm();
  const [isLoading, setLoading] = useState(false);
  const [htmlContent, setHtmlContent] = useState("");
  const [previewMode, setPreviewMode] = useState(false);

  // Định nghĩa các danh mục mẫu email
  const TEMPLATE_CATEGORIES = [
    { value: "welcome", label: t("TEMPLATE_CATEGORY_WELCOME") },
    { value: "promotion", label: t("TEMPLATE_CATEGORY_PROMOTION") },
    { value: "reminder", label: t("TEMPLATE_CATEGORY_REMINDER") },
    { value: "notification", label: t("TEMPLATE_CATEGORY_NOTIFICATION") },
    { value: "other", label: t("TEMPLATE_CATEGORY_OTHER") }
  ];

  // Định nghĩa các biến có sẵn
  const AVAILABLE_VARIABLES = [
    { name: "{{user_name}}", description: t("VARIABLE_USER_NAME") },
    { name: "{{user_email}}", description: t("VARIABLE_USER_EMAIL") },
    { name: "{{registration_date}}", description: t("VARIABLE_REGISTRATION_DATE") },
    { name: "{{package_name}}", description: t("VARIABLE_PACKAGE_NAME") },
    { name: "{{package_expiration_date}}", description: t("VARIABLE_PACKAGE_EXPIRATION_DATE") },
    { name: "{{company_name}}", description: t("VARIABLE_COMPANY_NAME") },
    { name: "{{support_email}}", description: t("VARIABLE_SUPPORT_EMAIL") },
    { name: "{{unsubscribe_link}}", description: t("VARIABLE_UNSUBSCRIBE_LINK") }
  ];

  // Lấy dữ liệu mẫu email khi id thay đổi
  useEffect(() => {
    if (!isCreating) {
      getTemplateData();
    } else {
      // Đặt giá trị mặc định cho mẫu mới
      form.setFieldsValue({
        category: "welcome"
      });
      setHtmlContent("");
      setLoading(false);
    }
  }, [id]);

  const getTemplateData = async () => {
    setLoading(true);
    try {
      const data = await getTemplateDetail(id);
      if (data) {
        form.setFieldsValue(data);
        setHtmlContent(data.content || "");
      } else {
        toast.error(t("ERROR_FETCHING_TEMPLATE"));
      }
    } catch (error) {
      console.error('Error loading template data:', error);
      toast.error(t("ERROR_FETCHING_TEMPLATE"));
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (values) => {
    try {
      setLoading(true);
      const saveData = {
        ...values,
        content: htmlContent
      };

      let response;
      if (isCreating) {
        response = await createTemplate(saveData);
        if (response) {
          toast.success(t("CREATE_TEMPLATE_SUCCESS"));
          navigate(LINK.ADMIN.EMAIL_TEMPLATE);
        } else {
          toast.error(t("CREATE_TEMPLATE_ERROR"));
        }
      } else {
        saveData._id = id;
        response = await updateTemplate(saveData);
        if (response) {
          toast.success(t("UPDATE_TEMPLATE_SUCCESS"));
          navigate(LINK.ADMIN.EMAIL_TEMPLATE);
        } else {
          toast.error(t("UPDATE_TEMPLATE_ERROR"));
        }
      }
    } catch (error) {
      console.error('Error saving template:', error);
      toast.error(t("ERROR_SAVING_TEMPLATE"));
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate(LINK.ADMIN.EMAIL_TEMPLATE);
  };

  const insertVariable = (variable) => {
    // Thêm biến vào nội dung HTML tại vị trí con trỏ
    setHtmlContent((prevContent) => {
      return prevContent + " " + variable.name + " ";
    });
  };

  const togglePreview = () => {
    setPreviewMode(!previewMode);
  };

  const isCreating = !id || id === "create";

  return (
    <Loading active={isLoading} transparent>
      <div className="email-template-detail-container">
        <div className="email-template-detail__header">
          <div className="header-content">
            <div className="header-title">{isCreating ? t("CREATE_NEW_TEMPLATE") : t("EDIT_TEMPLATE")}</div>
            <div className="header-description">{t("EMAIL_TEMPLATE_MANAGEMENT_DESCRIPTION")}</div>
          </div>
        </div>

        <div className="template-info-container">
          <div className="info-header">
            <h3 className="section-title">{t("TEMPLATE_INFORMATION")}</h3>
          </div>
          <div className="info-content">
            <AntForm
              form={form}
              onFinish={handleSave}
              layout="vertical"
              requiredMark={true}
              className="form-template-info"
            >
              <div className="form-row">
                <AntForm.Item
                  name="name"
                  label={t("TEMPLATE_NAME")}
                  rules={[{ required: true, message: t("PLEASE_ENTER_TEMPLATE_NAME") }]}
                  className="title-field"
                >
                  <Input placeholder={t("ENTER_TEMPLATE_NAME")} />
                </AntForm.Item>

                <AntForm.Item
                  name="category"
                  label={t("CATEGORY")}
                  rules={[{ required: true, message: t("PLEASE_SELECT_CATEGORY") }]}
                  className="category-field"
                >
                  <Select
                    placeholder={t("SELECT_CATEGORY")}
                    options={TEMPLATE_CATEGORIES}
                  />
                </AntForm.Item>
              </div>

              <div className="form-row">
                <AntForm.Item
                  name="subject"
                  label={t("EMAIL_SUBJECT")}
                  rules={[{ required: true, message: t("PLEASE_ENTER_EMAIL_SUBJECT") }]}
                  className="title-field"
                >
                  <Input placeholder={t("ENTER_EMAIL_SUBJECT")} />
                </AntForm.Item>
              </div>
            </AntForm>
          </div>
        </div>

        <div className="template-content-container">
          <div className="info-header">
            <h3 className="section-title">{t("CONTENT")}</h3>
          </div>
          <div className="info-content">
            <div className="email-variables-container">
              <div className="email-variables-title">{t("AVAILABLE_VARIABLES")}:</div>
              <div className="email-variables-buttons">
                {AVAILABLE_VARIABLES.map((variable, index) => (
                  <Button
                    key={index}
                    size="small"
                    onClick={() => insertVariable(variable)}
                    title={variable.description}
                    className="variable-button"
                  >
                    {variable.name}
                  </Button>
                ))}
              </div>
            </div>

            <div className="email-preview-toggle">
              <AntButton
                onClick={togglePreview}
                type={BUTTON.DEEP_NAVY}
                size="middle"
                className="preview-toggle-button"
              >
                {previewMode ? t("EDIT_MODE") : t("PREVIEW_MODE")}
              </AntButton>
            </div>

            {previewMode ? (
              <div
                className="email-preview"
                dangerouslySetInnerHTML={{ __html: htmlContent }}
              />
            ) : (
              <Input.TextArea
                value={htmlContent}
                onChange={(e) => setHtmlContent(e.target.value)}
                autoSize={{ minRows: 10, maxRows: 20 }}
                placeholder={t("ENTER_EMAIL_CONTENT_HTML")}
                className="email-content-editor"
              />
            )}
          </div>
        </div>

        <div className="template-content-container">
          <div className="info-header">
            <h3 className="section-title">{t("TEST_SEND")}</h3>
          </div>
          <div className="info-content">
            <div className="test-email-container">
              <div className="test-email-label">{t("TEST_EMAIL")}</div>
              <div className="test-email-input-group">
                <Input
                  placeholder={t("ENTER_TEST_EMAIL")}
                  className="test-email-input"
                  name="testEmail"
                />
                <AntButton
                  type={BUTTON.DEEP_NAVY}
                  size="middle"
                  className="test-email-button"
                >
                  {t("SEND_TEST_EMAIL")}
                </AntButton>
              </div>
            </div>
          </div>
        </div>

        <div className="save-button-container">
          <AntButton
            type={BUTTON.LIGHT_NAVY}
            size="large"
            onClick={handleCancel}
            style={{ marginRight: '16px' }}
            className="cancel-button"
          >
            {t("CANCEL")}
          </AntButton>
          <AntButton
            type={BUTTON.DEEP_NAVY}
            size="large"
            onClick={handleSave}
            className="save-button"
            icon={<SaveOutlined />}
          >
            {isCreating ? t("CREATE") : t("SAVE")}
          </AntButton>
        </div>
      </div>
    </Loading>
  );
};

export default EmailTemplateDetail;
