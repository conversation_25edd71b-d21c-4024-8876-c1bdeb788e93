.email-template-detail-container {
  background-color: var(--white);
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: Segoe UI;

  .email-template-detail__header {
    padding: 16px 20px;
    border-radius: 8px;
    background-color: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    position: relative;
    display: flex;
    align-items: center;
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .header-content {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .header-title {
        font-weight: 700;
        font-size: 20px;
        line-height: 28px;
        color: var(--typo-colours-primary-black);
      }

      .header-description {
        font-size: 14px;
        line-height: 20px;
        color: var(--typo-colours-secondary-grey);
      }
    }
  }

  // Shared styles for both template info and content containers
  .template-info-container,
  .template-content-container {
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    border-radius: 16px;
    padding: 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    overflow: hidden;

    &:hover {
      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
    }

    .info-header {
      background-color: #f8fafc;
      padding: 20px 32px;
      border-bottom: 1px solid #edf2f7;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #1f1f1f;
        margin: 0;
        display: flex;
        align-items: center;

        &:before {
          content: '';
          display: inline-block;
          width: 4px;
          height: 18px;
          background-color: var(--primary-color);
          margin-right: 12px;
          border-radius: 2px;
        }
      }
    }

    .info-content {
      padding: 32px;

      // Shared form styles
      .form-template-info {
        display: flex;
        flex-direction: column;
        gap: 30px;

        .form-row {
          display: flex;
          flex-direction: row;
          gap: 24px;
          align-items: flex-start;

          .title-field {
            flex: 2;
          }

          .category-field {
            flex: 1;
          }
        }

        .ant-form-item {
          margin: 0;
          width: 100%;

          .ant-form-item-label > label {
            font-weight: 600;
            color: #333;
            font-size: 15px;
          }

          .ant-input, .ant-select {
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s;

            &:hover, &:focus {
              border-color: var(--primary-color);
              box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
            }
          }

          .ant-input {
            height: 46px;
          }

          .ant-select-selector {
            border-radius: 8px;
            height: 46px !important;
            display: flex;
            align-items: center;
          }

          .ant-select-selection-item {
            line-height: 46px;
          }
        }
      }
    }
  }

  // Email content specific styles
  .template-content-container {
    .email-variables-container {
      margin-bottom: 20px;
      padding: 16px;
      background-color: #f8fafc;
      border-radius: 8px;
      border: 1px solid #edf2f7;

      .email-variables-title {
        font-weight: 600;
        margin-bottom: 12px;
        color: #333;
      }

      .email-variables-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 12px;

        .variable-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px;
          background-color: #ffffff;
          border-radius: 6px;
          border: 1px solid #e9ecef;

          .variable-button {
            background-color: #e6f7ff;
            border-color: #91d5ff;
            color: #1890ff;
            font-family: 'Courier New', monospace;
            min-width: 120px;
            font-size: 12px;

            &:hover {
              background-color: #bae7ff;
              border-color: #1890ff;
              transform: translateY(-1px);
              box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
            }
          }

          .variable-description {
            font-size: 13px;
            color: #6c757d;
            flex: 1;
          }
        }
      }
    }

    .test-email-container {
      padding: 16px 0;

      .test-email-label {
        font-weight: 600;
        margin-bottom: 12px;
        color: #333;
        font-size: 15px;
      }

      .test-email-input-group {
        display: flex;
        gap: 16px;

        .test-email-input {
          flex: 1;
          height: 46px;
          border-radius: 8px;
          border: 1px solid #e2e8f0;

          &:hover, &:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
          }
        }

        .test-email-button {
          min-width: 150px;
          border-radius: 8px;
          height: 46px;
          padding: 0 16px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .email-preview-toggle {
      display: flex;
      justify-content: flex-end;
      margin-bottom: 16px;

      .preview-toggle-button {
        border-radius: 8px;
        height: 40px;
        padding: 0 16px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .email-content-editor {
      border-radius: 8px;
      border: 1px solid #e2e8f0;
      min-height: 300px;
      font-family: monospace;
      padding: 16px;

      &:hover, &:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      }
    }

    .email-preview {
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 16px;
      min-height: 300px;
      background-color: #fff;
      overflow: auto;
    }
  }

  .save-button-container {
    display: flex;
    justify-content: center;
    margin: 24px 0;
    width: 100%;

    .save-button,
    .cancel-button {
      min-width: 180px;
      height: 48px;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      border-radius: 6px;
      transition: all 0.3s ease;
    }

    .save-button {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }

    .cancel-button {
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

      &:hover {
        transform: translateY(-2px);
      }
    }
  }

  // Preview Modal Styles
  .email-preview-modal {
    .preview-modal-content {
      .preview-data-form {
        margin-bottom: 24px;
        padding: 16px;
        background-color: #f8fafc;
        border-radius: 8px;
        border: 1px solid #edf2f7;

        h4 {
          margin-bottom: 16px;
          color: #333;
          font-weight: 600;
        }

        .preview-input-group {
          .preview-label {
            display: block;
            margin-bottom: 4px;
            font-size: 12px;
            color: #666;
            font-weight: 500;
          }

          .ant-input {
            border-radius: 4px;
            border: 1px solid #d9d9d9;

            &:hover, &:focus {
              border-color: #1890ff;
            }
          }
        }
      }

      .preview-email-content {
        h4 {
          margin-bottom: 16px;
          color: #333;
          font-weight: 600;
        }

        .preview-subject {
          margin-bottom: 16px;
          padding: 12px;
          background-color: #f0f2f5;
          border-radius: 6px;
          border-left: 4px solid #1890ff;

          strong {
            color: #333;
          }

          span {
            color: #666;
            margin-left: 8px;
          }
        }

        .preview-content {
          .email-content-preview {
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            padding: 20px;
            background-color: #ffffff;
            min-height: 200px;
            max-height: 400px;
            overflow-y: auto;
            font-family: Arial, sans-serif;
            line-height: 1.6;

            // Email content styling
            h1, h2, h3, h4, h5, h6 {
              margin-top: 0;
              margin-bottom: 16px;
              color: #333;
            }

            p {
              margin-bottom: 12px;
              color: #555;
            }

            a {
              color: #1890ff;
              text-decoration: none;

              &:hover {
                text-decoration: underline;
              }
            }

            img {
              max-width: 100%;
              height: auto;
            }

            table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 16px;

              th, td {
                padding: 8px 12px;
                border: 1px solid #e8e8e8;
                text-align: left;
              }

              th {
                background-color: #f5f5f5;
                font-weight: 600;
              }
            }
          }
        }
      }
    }
  }
}
